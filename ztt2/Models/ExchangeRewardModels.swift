//
//  ExchangeRewardModels.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation

/**
 * 兑换奖品数据模型
 */
struct ExchangeReward: Identifiable, Hashable {
    let id: UUID
    let name: String        // 奖品名称
    let cost: Int          // 消耗积分
    
    init(id: UUID = UUID(), name: String, cost: Int) {
        self.id = id
        self.name = name
        self.cost = cost
    }
}

/**
 * 兑换表单项数据模型
 */
struct ExchangeRewardFormItem: Identifiable, Hashable {
    let id: UUID
    var name: String        // 奖品名称
    var cost: String        // 消耗积分（字符串形式，用于输入）
    
    init(id: UUID = UUID(), name: String = "", cost: String = "") {
        self.id = id
        self.name = name
        self.cost = cost
    }
    
    /**
     * 验证表单项是否有效
     */
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !cost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               Int(cost) != nil &&
               Int(cost)! > 0
    }
    
    /**
     * 获取验证错误信息
     */
    var validationError: String? {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedCost = cost.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedName.isEmpty {
            return "请输入奖品名称"
        }
        
        if trimmedCost.isEmpty {
            return "请输入消耗积分"
        }
        
        guard let costValue = Int(trimmedCost) else {
            return "积分必须是数字"
        }
        
        if costValue <= 0 {
            return "积分必须大于0"
        }
        
        return nil
    }
}

/**
 * 兑换表单数据模型
 */
struct ExchangeRewardFormData {
    var items: [ExchangeRewardFormItem]
    
    init() {
        self.items = [ExchangeRewardFormItem()]
    }
    
    /**
     * 是否可以删除表单项
     */
    var canDeleteItems: Bool {
        return items.count > 1
    }
    
    /**
     * 是否可以添加表单项
     */
    var canAddItems: Bool {
        return items.count < 5
    }
    
    /**
     * 添加新的表单项
     */
    mutating func addItem() {
        if canAddItems {
            items.append(ExchangeRewardFormItem())
        }
    }
    
    /**
     * 删除指定索引的表单项
     */
    mutating func removeItem(at index: Int) {
        if canDeleteItems && index < items.count {
            items.remove(at: index)
        }
    }
    
    /**
     * 验证所有表单项
     */
    func validateItems() -> ExchangeRewardFormValidationResult {
        var errorMessages: [String] = []
        var hasValidItems = false
        
        for (index, item) in items.enumerated() {
            let trimmedName = item.name.trimmingCharacters(in: .whitespacesAndNewlines)
            let trimmedCost = item.cost.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 如果名称和积分都为空，跳过验证（允许空行）
            if trimmedName.isEmpty && trimmedCost.isEmpty {
                continue
            }
            
            // 如果有任何内容，则进行完整验证
            if let error = item.validationError {
                errorMessages.append("第\(index + 1)项：\(error)")
            } else {
                hasValidItems = true
            }
        }
        
        // 如果没有任何有效项，添加错误
        if !hasValidItems {
            errorMessages.append("请至少添加一个有效的奖品")
        }
        
        return ExchangeRewardFormValidationResult(
            isValid: errorMessages.isEmpty && hasValidItems,
            errorMessages: errorMessages
        )
    }
    
    /**
     * 获取有效的表单项
     */
    var validItems: [ExchangeRewardFormItem] {
        return items.filter { $0.isValid }
    }
    
    /**
     * 计算总消耗积分
     */
    var totalCost: Int {
        return validItems.compactMap { Int($0.cost) }.reduce(0, +)
    }
}

/**
 * 兑换表单验证结果
 */
struct ExchangeRewardFormValidationResult {
    let isValid: Bool
    let errorMessages: [String]
}

/**
 * 兑换记录数据模型
 */
struct ExchangeRecord: Identifiable, Hashable {
    let id: UUID
    let rewardName: String      // 奖品名称
    let cost: Int              // 消耗积分
    let timestamp: Date        // 兑换时间
    let memberName: String     // 成员名称
    
    init(id: UUID = UUID(), rewardName: String, cost: Int, timestamp: Date = Date(), memberName: String) {
        self.id = id
        self.rewardName = rewardName
        self.cost = cost
        self.timestamp = timestamp
        self.memberName = memberName
    }
    
    /**
     * 格式化显示的时间
     */
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: timestamp)
    }
    
    /**
     * 格式化显示的积分
     */
    var formattedCost: String {
        return "-\(cost)"
    }
}
