//
//  MemberRewardExchangeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 成员兑换奖品弹窗组件
 * 基于MemberPointsOptionsView设计，适配奖品兑换场景
 *
 * ## 功能特性
 * - 显示预设的奖品列表
 * - 支持直接兑换奖品（扣除积分）
 * - 支持添加新奖品
 * - 美观的动画效果和触觉反馈
 *
 * ## 设计模式
 * 采用组合模式，将奖品选择和添加奖品分离
 *
 * ## 兼容性
 * - iOS 15.6+
 * - 支持深色模式和动态字体
 * - 完整的无障碍支持
 */
struct MemberRewardExchangeView: View {

    // MARK: - Properties
    @Binding var isPresented: Bool
    let memberName: String
    let currentPoints: Int
    let rewards: [MemberReward]
    let onRewardSelected: (MemberReward) -> Void
    let onAddRewardRequested: () -> Void
    let onRewardDeleted: ((MemberReward) -> Void)?
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    @State private var showFirstTimeGuide = false

    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 兑换奖品对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 当前积分显示
                        currentPointsSection
                        
                        // 分隔线
                        dividerView
                        
                        // 奖品列表区域
                        if !rewards.isEmpty {
                            rewardsSection
                        } else {
                            emptyRewardsSection
                        }
                        
                        // 底部间距
                        Spacer()
                            .frame(height: 10)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 350))
                    .frame(maxHeight: min(geometry.size.height * 0.7, 500))
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#B5E36B").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }

                // 如果是首次使用且无奖品，显示引导
                if rewards.isEmpty {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        showFirstTimeGuide = true
                    }
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
        .alert("欢迎使用奖品兑换", isPresented: $showFirstTimeGuide) {
            Button("开始配置") {
                showFirstTimeGuide = false
                onAddRewardRequested()
            }
            Button("稍后配置", role: .cancel) {
                showFirstTimeGuide = false
            }
        } message: {
            Text("您可以为\(memberName)创建专属的奖品列表，让积分兑换更加有趣和个性化。")
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            Text("兑换奖品")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 当前积分显示区域
     */
    private var currentPointsSection: some View {
        HStack {
            Text("\(memberName)的当前积分")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Spacer()

            Text("\(currentPoints) 分")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.scoreDisplay)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }

    /**
     * 奖品列表区域
     */
    private var rewardsSection: some View {
        VStack(spacing: 0) {
            // 区域标题
            HStack {
                Text("可兑换奖品")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 添加奖品按钮
                Button(action: {
                    // 触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    onAddRewardRequested()
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(Color(hex: "#B5E36B"))
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 12)

            // 奖品列表 - 使用List支持swipeActions
            List {
                ForEach(rewards) { reward in
                    MemberRewardOptionButton(
                        reward: reward,
                        currentPoints: currentPoints,
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                onRewardSelected(reward)
                            }
                        }
                    )
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets(top: 4, leading: 0, bottom: 4, trailing: 0))
                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                        Button(role: .destructive) {
                            // 直接删除奖品
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                onRewardDeleted?(reward)
                            }

                            // 触觉反馈
                            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                            impactFeedback.impactOccurred()
                        } label: {
                            Label("删除", systemImage: "trash")
                        }
                        .tint(.red)
                    }
                }
            }
            .listStyle(PlainListStyle())
            .frame(maxHeight: 200)
            .padding(.horizontal, 20)
            .background(Color.clear) // iOS 15.6兼容的背景设置
        }
    }

    /**
     * 空奖品状态
     */
    private var emptyRewardsSection: some View {
        VStack(spacing: 12) {
            Image("lingqujilu")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .foregroundColor(Color.gray.opacity(0.5))

            Text("暂无可兑换奖品")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.gray)
                .multilineTextAlignment(.center)

            // 添加按钮
            Button(action: {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                onAddRewardRequested()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 16))
                    Text("添加奖品")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(Color(hex: "#B5E36B"))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(hex: "#B5E36B").opacity(0.1))
                .cornerRadius(20)
            }
            .padding(.top, 8)
        }
        .padding(.vertical, 24)
    }
}

/**
 * 成员奖品选项按钮组件
 */
struct MemberRewardOptionButton: View {

    let reward: MemberReward
    let currentPoints: Int
    let action: () -> Void

    @State private var isPressed = false

    // 计算是否可以兑换
    private var canExchange: Bool {
        return currentPoints >= reward.pointsCost
    }

    var body: some View {
        Button(action: {
            if canExchange {
                action()
            } else {
                // 积分不足时的触觉反馈
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.warning)
            }
        }) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(canExchange ? Color(hex: "#B5E36B").opacity(0.15) : Color.gray.opacity(0.1))
                        .frame(width: 40, height: 40)

                    Image("lingqujilu")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 18, height: 18)
                        .foregroundColor(canExchange ? Color(hex: "#B5E36B") : Color.gray.opacity(0.6))
                }

                // 奖品信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(reward.name)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(canExchange ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)

                    // 积分消耗
                    Text("\(reward.pointsCost) 分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(canExchange ? Color(hex: "#B5E36B") : Color.gray)

                    // 描述信息（如果有）
                    if let description = reward.description, !description.isEmpty {
                        Text(description)
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                // 右侧状态
                if canExchange {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.gray.opacity(0.6))
                } else {
                    Text("积分不足")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.gray)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isPressed && canExchange
                        ? Color(hex: "#B5E36B").opacity(0.1)
                        : Color.clear
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        canExchange
                        ? Color(hex: "#B5E36B").opacity(0.2)
                        : Color.gray.opacity(0.2),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed && canExchange ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            if canExchange {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            if canExchange {
                isPressed = pressing
            }
        }, perform: {})
        .disabled(!canExchange)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        MemberRewardExchangeView(
            isPresented: .constant(true),
            memberName: "多多",
            currentPoints: 15,
            rewards: [
                MemberReward(name: "小玩具", pointsCost: 10, description: "精美的小玩具一个"),
                MemberReward(name: "零食大礼包", pointsCost: 20, description: "各种美味零食"),
                MemberReward(name: "游戏时间", pointsCost: 5, description: "额外30分钟游戏时间")
            ],
            onRewardSelected: { reward in
                print("选择奖品: \(reward.name)")
            },
            onAddRewardRequested: {
                print("请求添加奖品")
            },
            onRewardDeleted: { reward in
                print("删除奖品: \(reward.name)")
            },
            onCancel: {
                print("取消操作")
            }
        )
    }
}
