//
//  ExchangeRewardFormView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 添加奖品表单弹窗组件
 * 支持添加多个奖品，包含表单验证和错误提示
 */
struct ExchangeRewardFormView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let onSubmit: (ExchangeRewardFormData) -> Void
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var formData = ExchangeRewardFormData()
    @State private var focusedField: UUID?
    @State private var showValidationErrors = false
    @State private var validationResult: ExchangeRewardFormValidationResult?
    @State private var isSubmitting = false
    
    // MARK: - Initialization
    init(
        isPresented: Binding<Bool>,
        onSubmit: @escaping (ExchangeRewardFormData) -> Void,
        onCancel: @escaping () -> Void
    ) {
        self._isPresented = isPresented
        self.onSubmit = onSubmit
        self.onCancel = onCancel
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismissKeyboard()
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
            }
            
            // 主弹窗内容
            if isPresented {
                VStack(spacing: 0) {
                    Spacer()
                    
                    // 弹窗卡片
                    VStack(spacing: 0) {
                        // 顶部标题栏
                        headerView
                        
                        // 内容区域
                        contentView
                        
                        // 底部按钮区域
                        bottomButtonsView
                    }
                    .background(Color.white)
                    .cornerRadius(20)
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -2)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented)
        .onTapGesture {
            dismissKeyboard()
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            // 标题
            Text("添加奖品")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 添加项目按钮
            if formData.canAddItems {
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        addNewItem()
                    }
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(hex: "#87C441"))
                        .frame(width: 32, height: 32)
                        .background(Color(hex: "#87C441").opacity(0.1))
                        .clipShape(Circle())
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(hex: "#f8f9fa"))
    }
    
    // MARK: - Content View
    private var contentView: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 说明文字
                HStack {
                    Text("添加新的奖品到兑换列表中，最多可添加5个奖品")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                
                // 表单项列表
                VStack(spacing: 12) {
                    ForEach(Array(formData.items.enumerated()), id: \.element.id) { index, item in
                        ExchangeFormItemRow(
                            item: binding(for: item),
                            index: index,
                            canDelete: formData.canDeleteItems,
                            focusedField: $focusedField,
                            onDelete: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    removeItem(at: index)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                
                // 验证错误显示
                if showValidationErrors, let result = validationResult, !result.isValid {
                    ExchangeValidationErrorsView(errorMessages: result.errorMessages)
                        .padding(.horizontal, 20)
                        .transition(.scale.combined(with: .opacity))
                }
                
                // 预览信息
                if formData.validItems.count > 0 {
                    ExchangePreviewView(formData: formData)
                        .padding(.horizontal, 20)
                        .transition(.scale.combined(with: .opacity))
                }
            }
        }
        .frame(maxHeight: 500)
    }
    
    // MARK: - Bottom Buttons View
    private var bottomButtonsView: some View {
        HStack(spacing: 12) {
            // 取消按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("取消")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color(hex: "#f5f5f5"))
                    .cornerRadius(12)
            }
            
            // 确认按钮
            Button(action: {
                submitForm()
            }) {
                HStack(spacing: 8) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                    
                    Text(isSubmitting ? "添加中..." : "确认添加")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(Color(hex: "#87C441"))
                .cornerRadius(12)
            }
            .disabled(isSubmitting)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    // MARK: - Helper Methods
    
    /**
     * 获取指定表单项的绑定
     */
    private func binding(for item: ExchangeRewardFormItem) -> Binding<ExchangeRewardFormItem> {
        guard let index = formData.items.firstIndex(where: { $0.id == item.id }) else {
            fatalError("无法找到表单项")
        }
        return $formData.items[index]
    }
    
    /**
     * 添加新的表单项
     */
    private func addNewItem() {
        formData.addItem()
        
        // 隐藏验证错误
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            showValidationErrors = false
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 删除指定索引的表单项
     */
    private func removeItem(at index: Int) {
        formData.removeItem(at: index)
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /**
     * 关闭键盘
     */
    private func dismissKeyboard() {
        focusedField = nil
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    /**
     * 提交表单
     */
    private func submitForm() {
        dismissKeyboard()

        // 验证表单
        let result = formData.validateItems()
        validationResult = result

        if !result.isValid {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                showValidationErrors = true
            }

            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }

        // 提交数据
        isSubmitting = true

        // 模拟提交延迟（实际使用中可以移除）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isSubmitting = false

            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)

            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                onSubmit(formData)
            }
        }
    }
}

/**
 * 兑换表单项行组件
 */
struct ExchangeFormItemRow: View {
    @Binding var item: ExchangeRewardFormItem
    let index: Int
    let canDelete: Bool
    @Binding var focusedField: UUID?
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 标题
            HStack {
                Text("奖品 \(index + 1)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 删除按钮
                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.red)
                    }
                }
            }
            
            // 输入字段
            VStack(spacing: 12) {
                // 奖品名称输入
                VStack(alignment: .leading, spacing: 6) {
                    Text("奖品名称")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    TextField("请输入奖品名称", text: $item.name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .focused($focusedField, equals: item.id)
                }
                
                // 消耗积分输入
                VStack(alignment: .leading, spacing: 6) {
                    Text("消耗积分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    TextField("请输入消耗积分", text: $item.cost)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                        .focused($focusedField, equals: UUID()) // 使用不同的UUID避免冲突
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: "#f8f9fa"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "#87C441").opacity(0.2), lineWidth: 1)
                )
        )
    }
}

/**
 * 验证错误显示组件
 */
struct ExchangeValidationErrorsView: View {
    let errorMessages: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.red)
                
                Text("请修正以下错误:")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red)
            }
            
            ForEach(errorMessages, id: \.self) { message in
                Text("• \(message)")
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.red)
                    .padding(.leading, 24)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

/**
 * 预览信息组件
 */
struct ExchangePreviewView: View {
    let formData: ExchangeRewardFormData
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "eye.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "#87C441"))
                
                Text("预览:")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(hex: "#87C441"))
            }
            
            Text("将添加 \(formData.validItems.count) 个奖品")
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(.leading, 24)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: "#87C441").opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "#87C441").opacity(0.2), lineWidth: 1)
                )
        )
    }
}
