//
//  ExchangeRewardsOptionsView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 兑换奖品选项弹窗组件
 * 显示预设的奖品列表和自定义选项，支持添加新奖品
 */
struct ExchangeRewardsOptionsView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let rewards: [ExchangeReward]
    let currentPoints: Int
    let onRewardSelected: (ExchangeReward) -> Void
    let onCustomSelected: () -> Void
    let onAddRewardRequested: () -> Void
    let onRewardDeleted: ((ExchangeReward) -> Void)?
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    @State private var showFirstTimeGuide = false

    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
            }
            
            // 主弹窗内容
            if isPresented {
                VStack(spacing: 0) {
                    Spacer()
                    
                    // 弹窗卡片
                    VStack(spacing: 0) {
                        // 顶部标题栏
                        headerView
                        
                        // 内容区域
                        contentView
                        
                        // 底部按钮区域
                        bottomButtonsView
                    }
                    .background(Color.white)
                    .cornerRadius(20)
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -2)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented)
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            // 标题
            Text("兑换奖品")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 当前积分显示
            HStack(spacing: 4) {
                Text("当前积分:")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("\(currentPoints)")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(Color(hex: "#87C441"))
            }
            
            // 添加奖品按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    onAddRewardRequested()
                }
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "#87C441"))
                    .frame(width: 32, height: 32)
                    .background(Color(hex: "#87C441").opacity(0.1))
                    .clipShape(Circle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(hex: "#f8f9fa"))
    }
    
    // MARK: - Content View
    private var contentView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                // 预设奖品列表
                if !rewards.isEmpty {
                    ForEach(rewards, id: \.id) { reward in
                        ExchangeRewardRow(
                            reward: reward,
                            currentPoints: currentPoints,
                            onSelected: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    onRewardSelected(reward)
                                }
                            },
                            onDelete: onRewardDeleted != nil ? {
                                onRewardDeleted?(reward)
                            } : nil
                        )
                    }
                } else {
                    // 空状态
                    EmptyRewardsView(onAddReward: onAddRewardRequested)
                }
                
                // 自定义兑换选项
                ExchangeCustomOptionRow(
                    action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            onCustomSelected()
                        }
                    }
                )
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .frame(maxHeight: 400)
    }
    
    // MARK: - Bottom Buttons View
    private var bottomButtonsView: some View {
        HStack {
            // 取消按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("取消")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color(hex: "#f5f5f5"))
                    .cornerRadius(12)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
}

/**
 * 兑换奖品行组件
 */
struct ExchangeRewardRow: View {
    let reward: ExchangeReward
    let currentPoints: Int
    let onSelected: () -> Void
    let onDelete: (() -> Void)?
    
    @State private var isPressed = false
    
    private var canAfford: Bool {
        return currentPoints >= reward.cost
    }
    
    var body: some View {
        Button(action: {
            if canAfford {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onSelected()
            } else {
                // 积分不足的错误反馈
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)
            }
        }) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(canAfford ? Color(hex: "#87C441").opacity(0.15) : Color.gray.opacity(0.15))
                        .frame(width: 40, height: 40)

                    Image("lingqujilu")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 20, height: 20)
                        .foregroundColor(canAfford ? Color(hex: "#87C441") : Color.gray)
                }

                // 奖品信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(reward.name)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(canAfford ? DesignSystem.Colors.textPrimary : Color.gray)
                        .multilineTextAlignment(.leading)

                    Text("\(reward.cost) 积分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(canAfford ? Color(hex: "#87C441") : Color.gray)
                }

                Spacer()
                
                // 积分不足提示或删除按钮
                if !canAfford {
                    Text("积分不足")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.red)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                } else if let onDelete = onDelete {
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.red)
                    }
                    .padding(.trailing, 8)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(canAfford ? Color.white : Color.gray.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(canAfford ? Color(hex: "#87C441").opacity(0.2) : Color.gray.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .opacity(canAfford ? 1.0 : 0.6)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

/**
 * 自定义兑换选项行组件
 */
struct ExchangeCustomOptionRow: View {
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color(hex: "#f39c12").opacity(0.15))
                        .frame(width: 40, height: 40)

                    Image(systemName: "square.and.pencil")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: "#f39c12"))
                }

                // 自定义信息
                VStack(alignment: .leading, spacing: 4) {
                    Text("自定义兑换")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("输入自定义的奖品名称和消耗积分")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(hex: "#f39c12").opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

/**
 * 空奖品状态视图
 */
struct EmptyRewardsView: View {
    let onAddReward: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "gift")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(Color.gray.opacity(0.5))
            
            Text("还没有添加奖品")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("点击右上角 + 号添加第一个奖品")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
            
            Button(action: onAddReward) {
                Text("添加奖品")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(hex: "#87C441"))
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .background(Color(hex: "#87C441").opacity(0.1))
                    .cornerRadius(16)
            }
        }
        .padding(.vertical, 40)
    }
}
